import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@workspace/ui/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center justify-center rounded-full px-3 py-1 text-xs text-shadow-none w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1  transition-[color,box-shadow] overflow-hidden border",
  {
    variants: {
      variant: {
        default: "border-foreground bg-foreground/25 text-foreground",
        warning:
          "border-warning bg-warning/25 text-warning-foreground [a&]:hover:bg-warning/90",
        destructive:
          "border-destructive bg-destructive/25 text-destructive-foreground",
        success:
          "border-success bg-success/25 text-success-foreground [a&]:hover:bg-success/90",
        outline: "text-foreground",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

function Badge({
  className,
  variant,
  asChild = false,
  ...props
}: React.ComponentProps<"span"> &
  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {
  const Comp = asChild ? Slot : "span";

  return (
    <Comp
      data-slot="badge"
      className={cn(badgeVariants({ variant }), className)}
      {...props}
    />
  );
}

export { Badge, badgeVariants };
