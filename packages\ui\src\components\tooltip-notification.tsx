import * as React from "react";
import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON><PERSON>ontent,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";

export function TooltipNotification({
  children,
  message,
  open,
}: {
  children: React.ReactNode;
  message: string;
  open?: boolean;
}) {
  return (
    <Tooltip open={open}>
      <TooltipTrigger asChild>{children}</TooltipTrigger>
      <TooltipContent
        align="end"
        alignOffset={-10}
        className="bg-destructive rounded-xs -skew-x-12"
        arrowClassName="fill-destructive bg-destructive size-4"
      >
        <span className="skew-x-12">{message}</span>
      </TooltipContent>
    </Tooltip>
  );
}
