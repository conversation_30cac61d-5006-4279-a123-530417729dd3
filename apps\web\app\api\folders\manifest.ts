export type FolderManifest = Record<string, string[]>;

export const folderManifest: FolderManifest = {
  "/": [
    "buttons",
    "dialogs",
    "hover",
    "inputs",
    "popovers",
    "tabs"
  ],
  "/buttons": [
    "default",
    "default-and-primary",
    "disabled",
    "primary"
  ],
  "/dialogs": [
    "activity",
    "bank-deposit",
    "info",
    "kyc",
    "order",
    "profile",
    "wallet"
  ],
  "/hover": [
    "tooltips"
  ],
  "/inputs": [
    "nested-checkboxes",
    "validated-input"
  ],
  "/popovers": [
    "approval"
  ],
  "/tabs": [
    "four-tabs",
    "two-tabs"
  ]
} as const;
